@echo off
chcp 65001 >nul
title GoProxy 启动器

echo ========================================
echo GoProxy 透明代理启动器
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此批处理文件
    echo 2. 选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

REM 检查文件是否存在
if not exist goproxy.exe (
    echo ❌ 错误：找不到 goproxy.exe
    echo 请确保 goproxy.exe 与此脚本在同一目录
    echo.
    pause
    exit /b 1
)

echo ✅ 程序文件检查通过
echo.

REM 检查配置文件
if exist config.yaml (
    echo ✅ 找到配置文件: config.yaml
) else (
    echo ⚠️  未找到配置文件，将使用默认配置
)
echo.

echo 正在启动 GoProxy...
echo 如果程序立即退出，请查看错误信息
echo ========================================
echo.

REM 启动程序并捕获输出
goproxy.exe
set EXIT_CODE=%ERRORLEVEL%

echo.
echo ========================================
echo 程序已退出，退出代码: %EXIT_CODE%

if %EXIT_CODE% neq 0 (
    echo.
    echo ❌ 程序异常退出
    echo.
    echo 常见问题排查：
    echo 1. 确保以管理员身份运行
    echo 2. 检查防病毒软件是否阻止程序
    echo 3. 确保Windows版本为Windows 10/11
    echo 4. 检查网络配置是否正确
    echo.
    echo 如需调试，请运行：
    echo goproxy.exe -log-level debug
) else (
    echo ✅ 程序正常退出
)

echo.
pause
